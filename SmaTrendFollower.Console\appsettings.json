{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "UniverseCacheConnection": "localhost:6379"
  },
  "Cache": {
    "EnableRedisFirstCaching": true,
    "BackgroundPersistence": {
      "MarketHoursIntervalMinutes": 5,
      "OffHoursIntervalMinutes": 1,
      "MaxBatchSize": 25,
      "MaxConcurrentBatches": 2,
      "BatchProcessingDelayMs": 200,
      "MaxRetryAttempts": 3
    },
    "RedisTtl": {
      "PendingBarsHours": 24,
      "CachedBarsHours": 6,
      "BarSnapshotsMinutes": 30
    }
  },
  "SqlitePool": {
    "MaxReadConnections": 10,
    "InitialReadConnections": 3,
    "MaxWriteConnections": 1,
    "CommandTimeoutSeconds": 30,
    "BusyTimeoutMs": 5000
  },
  "Resilience": {
    "MaxRetryAttempts": 3,
    "BaseDelayMs": 1000,
    "BackoffMultiplier": 2.0,
    "CircuitBreakerFailureThreshold": 5,
    "CircuitBreakerDurationSeconds": 30
  },
  "ResourceMonitoring": {
    "MonitoringIntervalSeconds": 30,
    "EnablePeriodicLogging": true,
    "ManagedMemoryWarningMB": 1024,
    "ManagedMemoryCriticalMB": 2048,
    "WorkingSetWarningMB": 2048,
    "WorkingSetCriticalMB": 4096,
    "MemoryGrowthWarningBytes": 104857600,
    "ThreadCountWarning": 100,
    "ThreadCountCritical": 200,
    "CpuUsageWarningPercent": 80.0,
    "CpuUsageCriticalPercent": 95.0
  },
  "UniverseBuilding": {
    "EnableCachePreWarming": true,
    "PreWarmSymbolCount": 2000,
    "PreWarmDays": 60,
    "RequiredHistoryDays": 250,
    "MinRequiredBars": 200,
    "MinVolumeThreshold": 500000,
    "BatchSize": 100,
    "MaxConcurrentBatches": 8,
    "MaxConcurrencyPerBatch": 20,
    "MaxConcurrencyForCached": 50,
    "DelayBetweenSymbolsMs": 50
  },
  "Polygon": {
    "ApiKey": "********************************"
  },
  "Alpaca": {
    "Live": {
      "ApiKeyId": "AKGBPW5HD8LVI5C6NJUJ",
      "SecretKey": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"
    },
    "Paper": {
      "ApiKeyId": "PK0AM3WB1CES3YBQPGR0",
      "SecretKey": "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf"
    }
  },
  "Brave": {
    "SearchApiKey": "",
    "AiApiKey": "",
    "RateLimitPerSecond": 1,
    "MaxRequestsPerMonth": 2000,
    "TimeoutSeconds": 30
  },
  "Universe": {
    "UsePolygon": true,
    "MarketCapMin": 1000000000,
    "AvgVolumeMin": 100000,
    "MaxSymbols": 5000
  },
  "UniverseCache": {
    "RefreshInterval": "00:10:00"
  },

  // Enable Polygon-based universe management
  "UsePolygonUniverse": true,



  "ML": {
    "PositionModelPath": "Model/position_model.zip"
  },
  "SlippageTraining": {
    "ModelOutputPath": "Model/slippage_model.zip"
  },
  "VWAP": {
    "StartHour": 9,
    "StartMinute": 40
  },

  // Optimized Universe Building Configuration
  "OptimizedUniverse": {
    "MaxConcurrentRequests": 50,
    "SymbolTimeoutMs": 10000,
    "SlowRequestThresholdMs": 2000,
    "MinBarsRequired": 5
  },

  // Bulk Market Data Configuration
  "BulkData": {
    "MaxConcurrentRequests": 50,
    "SymbolTimeoutMs": 10000,
    "SlowRequestThresholdMs": 2000,
    "UseBulkEndpoints": true,
    "BulkThreshold": 100
  },

  // === Enhanced Services Configuration ===
  "EnhancedServices": {
    "EnableEnhancedDataRetrieval": true,
    "EnableAdaptiveRateLimit": true,
    "EnableAdaptiveSignalGeneration": true,
    "EnableEnhancedMetrics": true,
    "EnableSyntheticData": true,
    "EnableEmergencyMode": true
  },

  // Enhanced Data Retrieval Configuration
  "EnhancedDataRetrieval": {
    "MaxConcurrentRequests": 20,
    "PrimaryApiTimeout": "00:00:45",
    "BatchTimeout": "00:03:00",
    "RelaxedStalenessThreshold": "02:00:00",
    "EmergencyModeMaxStaleness": "1.00:00:00",
    "EmergencyModeTimeout": "00:15:00",
    "EnableSyntheticData": true,
    "MinimumBatchSuccessRate": 0.7,
    "MaxFailedAttempts": 3
  },

  // Synthetic Data Generation Configuration
  "SyntheticData": {
    "RandomSeed": null,
    "DefaultStartPrice": 100.0,
    "VolumeMultiplier": 0.5,
    "MaxCorrelation": 0.95,
    "MinCorrelation": 0.1,
    "DefaultVolatility": 0.02,
    "UseSectorCorrelations": true
  },

  // Flexible Data Staleness Configuration
  "FlexibleStaleness": {
    "MarketHours": {
      "HistoricalBars": "00:18:00",
      "RealTimeQuotes": "00:02:00",
      "IndexData": "00:15:00",
      "VixData": "00:15:00"
    },
    "AfterHours": {
      "HistoricalBars": "08:00:00",
      "RealTimeQuotes": "08:00:00",
      "IndexData": "08:00:00",
      "VixData": "08:00:00"
    },
    "EmergencyModeDuration": "00:15:00",
    "AllowCriticalOverrides": true,
    "AllowFallbackDataUsage": true,
    "LogStalenessWarnings": true
  },

  // Adaptive Rate Limiting Configuration
  "AdaptiveRateLimit": {
    "Providers": {
      "Alpaca": {
        "InitialLimit": 30,
        "MinLimit": 10,
        "MaxLimit": 60,
        "CircuitBreakerConfig": {
          "FailureThreshold": 8,
          "OpenTimeout": "00:03:00",
          "SuccessThreshold": 2
        }
      },
      "Polygon": {
        "InitialLimit": 40,
        "MinLimit": 15,
        "MaxLimit": 80,
        "CircuitBreakerConfig": {
          "FailureThreshold": 6,
          "OpenTimeout": "00:02:00",
          "SuccessThreshold": 2
        }
      }
    },
    "AdjustmentInterval": "00:03:00",
    "AdjustmentWindow": "00:08:00",
    "SuccessRateThreshold": 0.90,
    "AcquisitionTimeout": "00:01:00"
  },

  // Adaptive Signal Generation Configuration
  "AdaptiveSignal": {
    "MaxSymbolsToProcess": 500,
    "MaxConcurrentSymbols": 20,
    "MaxLookbackDays": 300,
    "MinimumBarsRequired": 10,
    "MinimumConfidenceScore": 0.5,
    "EnableSyntheticData": true,
    "EnableFallbackStrategies": true,
    "StrategyPreferences": {
      "FullSMAMinBars": 200,
      "AdaptedSMAMinBars": 100,
      "MomentumBasedMinBars": 50,
      "ShortTermTrendMinBars": 20,
      "PriceActionMinBars": 10,
      "ConfidenceMultipliers": {
        "FullSMA": 1.0,
        "AdaptedSMA": 0.9,
        "MomentumBased": 0.8,
        "ShortTermTrend": 0.7,
        "PriceAction": 0.6
      }
    }
  },

  // Robust Signal Generation Configuration
  "RobustSignal": {
    "MaxConcurrentGenerations": 5,
    "MinimumAcceptableSignals": 3,
    "MinimumConfidenceScore": 0.5,
    "EnableFallbackGeneration": true,
    "EnableEmergencyGeneration": true,
    "AdaptiveGenerationTimeout": "00:02:00",
    "FallbackGenerationTimeout": "00:01:00",
    "EmergencyModeDuration": "00:10:00",
    "MaxErrorsPerSymbol": 5,
    "ErrorCooldownPeriod": "00:30:00",
    "CoreSymbols": ["SPY", "QQQ", "IWM", "VTI", "VOO", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"],
    "RetryConfig": {
      "MaxRetries": 3,
      "BaseDelay": "00:00:01",
      "MaxDelay": "00:00:30",
      "UseExponentialBackoff": true,
      "UseJitter": true
    }
  },

  "AllowedHosts": "*"
}
